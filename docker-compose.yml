version: '3.8'

services:
  video-analysis-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: video-analysis-api
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OUR_OPENAI_API_KEY=${OUR_OPENAI_API_KEY}
    env_file:
      - .env
    volumes:
      # Mount uploads directory for persistent storage (optional)
      - ./uploads:/app/uploads
      # Mount saved_frames directory for debugging (optional)
      - ./saved_frames:/app/saved_frames
      # Mount logs directory (optional)
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - video-analysis-network

  # Optional: Add a reverse proxy (nginx) for production
  nginx:
    image: nginx:alpine
    container_name: video-analysis-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro  # For SSL certificates if needed
    depends_on:
      - video-analysis-api
    restart: unless-stopped
    networks:
      - video-analysis-network
    profiles:
      - production  # Only start with --profile production

networks:
  video-analysis-network:
    driver: bridge

volumes:
  uploads:
    driver: local
  saved_frames:
    driver: local
  logs:
    driver: local
