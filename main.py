from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException
from fastapi.responses import JSONResponse
import tempfile
import os
from pathlib import Path
import shutil
from listening_analysis import analyze_listening_video
from responding_analysis import analyze_responding_video

app = FastAPI(
    title="Video Analysis API",
    description="API for analyzing listening and responding skills in videos",
    version="1.0.0"
)

# Create uploads directory if it doesn't exist
UPLOAD_DIR = Path("uploads")
UPLOAD_DIR.mkdir(exist_ok=True)

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Video Analysis API",
        "endpoints": {
            "/analyze-listening": "POST - Analyze listening skills in video",
            "/analyze-responding": "POST - Analyze responding skills in video",
            "/docs": "GET - API documentation"
        }
    }

@app.post("/analyze-listening")
async def analyze_listening_endpoint(file: UploadFile = File(...)):
    """
    Analyze listening skills in a video file.
    
    Args:
        file: Video file to analyze
        
    Returns:
        JSON response with listening analysis results
    """
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")
    
    # Check if file is a video
    allowed_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'}
    file_extension = Path(file.filename).suffix.lower()
    
    if file_extension not in allowed_extensions:
        raise HTTPException(
            status_code=400, 
            detail=f"Unsupported file type. Allowed types: {', '.join(allowed_extensions)}"
        )
    
    try:
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
            temp_path = temp_file.name
            shutil.copyfileobj(file.file, temp_file)
        
        # Analyze the video
        result = analyze_listening_video(temp_path)
        
        # Clean up temporary file
        os.unlink(temp_path)
        
        return JSONResponse(content=result)
        
    except Exception as e:
        # Clean up temporary file if it exists
        if 'temp_path' in locals() and os.path.exists(temp_path):
            os.unlink(temp_path)
        
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

@app.post("/analyze-responding")
async def analyze_responding_endpoint(file: UploadFile = File(...)):
    """
    Analyze responding skills in a video file.
    
    Args:
        file: Video file to analyze
        
    Returns:
        JSON response with responding analysis results
    """
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")
    
    # Check if file is a video
    allowed_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'}
    file_extension = Path(file.filename).suffix.lower()
    
    if file_extension not in allowed_extensions:
        raise HTTPException(
            status_code=400, 
            detail=f"Unsupported file type. Allowed types: {', '.join(allowed_extensions)}"
        )
    
    try:
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
            temp_path = temp_file.name
            shutil.copyfileobj(file.file, temp_file)
        
        # Analyze the video
        result = analyze_responding_video(temp_path)
        
        # Clean up temporary file
        os.unlink(temp_path)
        
        return JSONResponse(content=result)
        
    except Exception as e:
        # Clean up temporary file if it exists
        if 'temp_path' in locals() and os.path.exists(temp_path):
            os.unlink(temp_path)
        
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "message": "Video Analysis API is running"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
