# Video Analysis API

A FastAPI server that provides endpoints for analyzing listening and responding skills in videos using AI.

## Features

- **Listening Analysis**: Analyzes non-verbal listening skills including eye contact, facial expressions, head/shoulder alignment, and micro-gestures
- **Responding Analysis**: Analyzes responding skills including visual cues, vocal characteristics, and content quality
- **File Upload Support**: Accepts various video formats (MP4, AVI, MOV, MKV, WMV, FLV, WebM)
- **RESTful API**: Clean REST endpoints with proper error handling
- **Interactive Documentation**: Automatic API documentation via FastAPI

## Setup

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Environment Variables**
   Create a `.env` file in the project root with your OpenAI API key:
   ```
   OPENAI_API_KEY=your_openai_api_key_here
   OUR_OPENAI_API_KEY=your_openai_api_key_here
   ```

3. **Run the Server**
   ```bash
   python main.py
   ```
   
   Or using uvicorn directly:
   ```bash
   uvicorn main:app --host 0.0.0.0 --port 8000 --reload
   ```

## API Endpoints

### Base URL: `http://localhost:8000`

- **GET /** - Root endpoint with API information
- **POST /analyze-listening** - Analyze listening skills in video
- **POST /analyze-responding** - Analyze responding skills in video  
- **GET /health** - Health check endpoint
- **GET /docs** - Interactive API documentation (Swagger UI)
- **GET /redoc** - Alternative API documentation

## Usage Examples

### Using curl

**Analyze Listening Skills:**
```bash
curl -X POST "http://localhost:8000/analyze-listening" \
     -H "accept: application/json" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@your_video.mp4"
```

**Analyze Responding Skills:**
```bash
curl -X POST "http://localhost:8000/analyze-responding" \
     -H "accept: application/json" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@your_video.mp4"
```

### Using Python requests

```python
import requests

# Analyze listening
with open('video.mp4', 'rb') as f:
    response = requests.post(
        'http://localhost:8000/analyze-listening',
        files={'file': f}
    )
    result = response.json()
    print(result)
```

## Response Format

### Listening Analysis Response
```json
{
  "listeningEvaluation": {
    "eyeContact": {
      "score": 4,
      "explanation": "Good eye contact maintained throughout"
    },
    "facialExpressions": {
      "score": 3,
      "explanation": "Neutral expressions, could be more engaging"
    },
    "headShouldersAlignment": {
      "score": 5,
      "explanation": "Excellent posture and alignment"
    },
    "microGestures": {
      "score": 4,
      "explanation": "Good use of supportive gestures"
    }
  },
  "overallScore": 4,
  "starRating": "★★★★☆",
  "strengths": "Strong posture and eye contact",
  "improvements": "More varied facial expressions would enhance engagement"
}
```

### Responding Analysis Response
```json
{
  "respondingEvaluation": {
    "visual": {
      "eyeContact": {"score": 4, "explanation": "..."},
      "facialExpressions": {"score": 3, "explanation": "..."},
      "headShouldersAlignment": {"score": 5, "explanation": "..."},
      "microGestures": {"score": 4, "explanation": "..."}
    },
    "vocal": {
      "toneEmotion": {"score": 4, "explanation": "..."},
      "volumeClarity": {"score": 5, "explanation": "..."},
      "paceRhythm": {"score": 3, "explanation": "..."}
    },
    "content": {
      "messageRelevanceAccuracy": {"score": 4, "explanation": "..."},
      "structureCoherence": {"score": 4, "explanation": "..."},
      "empathyWordChoice": {"score": 5, "explanation": "..."}
    }
  },
  "overallScore": 4,
  "starRating": "★★★★☆",
  "strengths": "Clear communication and empathetic responses",
  "improvements": "Work on pacing and rhythm"
}
```

## Requirements

- Python 3.8+
- OpenAI API key
- FFmpeg (for audio extraction)
- Sufficient disk space for temporary video processing

## Notes

- Videos are processed temporarily and deleted after analysis
- Large video files may take longer to process
- The API uses OpenAI's GPT-4o model for analysis
- Audio transcription uses OpenAI's Whisper model
