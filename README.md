# Video Analysis API

A FastAPI server that provides endpoints for analyzing listening and responding skills in videos using AI.

## Features

- **Listening Analysis**: Analyzes non-verbal listening skills including eye contact, facial expressions, head/shoulder alignment, and micro-gestures
- **Responding Analysis**: Analyzes responding skills including visual cues, vocal characteristics, and content quality
- **File Upload Support**: Accepts various video formats (MP4, AVI, MOV, MKV, WMV, FLV, WebM)
- **RESTful API**: Clean REST endpoints with proper error handling
- **Interactive Documentation**: Automatic API documentation via FastAPI

## Setup

### Option 1: Docker (Recommended)

1. **Clone and Setup Environment**

   ```bash
   # Copy environment template
   cp .env.example .env
   # Edit .env file with your OpenAI API keys
   ```

2. **Build and Run with Docker Compose**

   ```bash
   # Development mode (API only)
   docker-compose up --build

   # Production mode (with Nginx reverse proxy)
   docker-compose --profile production up --build
   ```

3. **Access the API**
   - Development: http://localhost:8000
   - Production: http://localhost (port 80)
   - API Documentation: http://localhost:8000/docs

### Option 2: Local Development

1. **Install Dependencies**

   ```bash
   pip install -r requirements.txt
   ```

2. **Environment Variables**
   Create a `.env` file in the project root with your OpenAI API key:

   ```
   OPENAI_API_KEY=your_openai_api_key_here
   OUR_OPENAI_API_KEY=your_openai_api_key_here
   ```

3. **Install System Dependencies**

   - **Ubuntu/Debian**: `sudo apt-get install ffmpeg`
   - **macOS**: `brew install ffmpeg`
   - **Windows**: Download from https://ffmpeg.org/

4. **Run the Server**

   ```bash
   python main.py
   ```

   Or using uvicorn directly:

   ```bash
   uvicorn main:app --host 0.0.0.0 --port 8000 --reload
   ```

## API Endpoints

### Base URL: `http://localhost:8000`

- **GET /** - Root endpoint with API information
- **POST /analyze-listening** - Analyze listening skills in video
- **POST /analyze-responding** - Analyze responding skills in video
- **GET /health** - Health check endpoint
- **GET /docs** - Interactive API documentation (Swagger UI)
- **GET /redoc** - Alternative API documentation

## Usage Examples

### Using curl

**Analyze Listening Skills:**

```bash
curl -X POST "http://localhost:8000/analyze-listening" \
     -H "accept: application/json" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@your_video.mp4"
```

**Analyze Responding Skills:**

```bash
curl -X POST "http://localhost:8000/analyze-responding" \
     -H "accept: application/json" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@your_video.mp4"
```

### Using Python requests

```python
import requests

# Analyze listening
with open('video.mp4', 'rb') as f:
    response = requests.post(
        'http://localhost:8000/analyze-listening',
        files={'file': f}
    )
    result = response.json()
    print(result)
```

## Response Format

### Listening Analysis Response

```json
{
  "listeningEvaluation": {
    "eyeContact": {
      "score": 4,
      "explanation": "Good eye contact maintained throughout"
    },
    "facialExpressions": {
      "score": 3,
      "explanation": "Neutral expressions, could be more engaging"
    },
    "headShouldersAlignment": {
      "score": 5,
      "explanation": "Excellent posture and alignment"
    },
    "microGestures": {
      "score": 4,
      "explanation": "Good use of supportive gestures"
    }
  },
  "overallScore": 4,
  "starRating": "★★★★☆",
  "strengths": "Strong posture and eye contact",
  "improvements": "More varied facial expressions would enhance engagement"
}
```

### Responding Analysis Response

```json
{
  "respondingEvaluation": {
    "visual": {
      "eyeContact": { "score": 4, "explanation": "..." },
      "facialExpressions": { "score": 3, "explanation": "..." },
      "headShouldersAlignment": { "score": 5, "explanation": "..." },
      "microGestures": { "score": 4, "explanation": "..." }
    },
    "vocal": {
      "toneEmotion": { "score": 4, "explanation": "..." },
      "volumeClarity": { "score": 5, "explanation": "..." },
      "paceRhythm": { "score": 3, "explanation": "..." }
    },
    "content": {
      "messageRelevanceAccuracy": { "score": 4, "explanation": "..." },
      "structureCoherence": { "score": 4, "explanation": "..." },
      "empathyWordChoice": { "score": 5, "explanation": "..." }
    }
  },
  "overallScore": 4,
  "starRating": "★★★★☆",
  "strengths": "Clear communication and empathetic responses",
  "improvements": "Work on pacing and rhythm"
}
```

## Docker Commands

### Basic Commands

```bash
# Build and start services
docker-compose up --build

# Run in background
docker-compose up -d

# Stop services
docker-compose down

# View logs
docker-compose logs -f video-analysis-api

# Rebuild after code changes
docker-compose up --build --force-recreate
```

### Production Deployment

```bash
# Start with Nginx reverse proxy
docker-compose --profile production up -d

# Scale the API service
docker-compose up --scale video-analysis-api=3
```

### Development

```bash
# Development with hot reload (mount source code)
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
```

## Requirements

- **Docker**: Docker 20.10+ and Docker Compose 2.0+
- **Local Development**: Python 3.8+, FFmpeg
- **API Keys**: OpenAI API key
- **Storage**: Sufficient disk space for temporary video processing

## Notes

- Videos are processed temporarily and deleted after analysis
- Large video files may take longer to process
- The API uses OpenAI's GPT-4o model for analysis
- Audio transcription uses OpenAI's Whisper model
- Docker containers include all system dependencies
- Nginx provides rate limiting and handles large file uploads
