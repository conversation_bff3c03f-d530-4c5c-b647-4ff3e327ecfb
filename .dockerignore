# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Test files
test_*.py
*_test.py
tests/
.pytest_cache/
.coverage
htmlcov/

# Documentation
docs/
*.md
!README.md

# Media files (exclude sample videos)
*.mp4
*.avi
*.mov
*.mkv
*.wmv
*.flv
*.webm

# Generated files
saved_frames/
uploads/
*.png
*.jpg
*.jpeg
*.wav

# Environment files (will be handled separately)
.env.example
